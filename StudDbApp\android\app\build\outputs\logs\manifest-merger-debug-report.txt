-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:1:1-26:12
MERGED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:1:1-26:12
INJECTED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\debug\AndroidManifest.xml:2:1-9:12
INJECTED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\debug\AndroidManifest.xml:2:1-9:12
INJECTED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\debug\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.react:react-android:0.80.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5d0a6fceadea979a29a63bd98787fde9\transformed\react-android-0.80.2-debug\AndroidManifest.xml:2:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f43af4eaa533d8678c3d8bedb383ac4e\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\97726a531d160aa8efd424c23b47b0e7\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bb1509f867cc8241c6321ad780ab9b40\transformed\fresco-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a09cdeb31c1a69b0827dc865c184afd8\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8eb01e9cfdf7fc987f8c5c9f670c8ca7\transformed\drawee-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\aa66091e09555cec570e10a7df1ce238\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bfc17559a383d0ef1d01959dc4d84b80\transformed\memory-type-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\374e69babad10f8ad164f484a043f5cb\transformed\memory-type-java-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c1754bbfa950701b23fefef6bf796ceb\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4ac248b3671de32448570b9644e52940\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2f98baf551fbb7a6d5cea4f26d7a8ec5\transformed\imagepipeline-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8e522eca61febf8143e236f9d72207df\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9377fa07eef938bc503b47a41797d20\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04248846d45c7715aa716d408915667f\transformed\urimod-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8b09f32e8fc80fb8f8e561c9746f2051\transformed\vito-source-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\565be54688d002c2e6b94da854cf01b0\transformed\middleware-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\438600a2e90ac0d8713b620024e63e26\transformed\ui-common-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8aa569497c94a1eda85ac3cb798a1ad7\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2a0da4efd780d1d387ac0b6a795ff313\transformed\activity-1.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c9e8870c85c6f7137997dc0b4d1a588e\transformed\autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\30c3f3a18f8f5396c69acef992d82cc6\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0bbf34456e237ce1a6931dbc99635f40\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\241e54545b49a632ffde793c1862f3d7\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fed6fbb2983fb7accf714336d7728246\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f9d6938b3bbb51e2b58870b44d6c01ef\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bc67299e5d7a731320ef200998f51e11\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f008d7eaa67ee21ccc9979bf9a561d17\transformed\soloader-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\50081fb9fd213c2025efe5a44258cd6a\transformed\fbcore-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec40a90505dc8ff078e92d445c422ca7\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\514f6bce29f0ded98ca5a500375fbcdb\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1326c6b094cf38787ffe987c60d9a487\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0397ba8402f4881c1bbcde129a842107\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0753d282651e5378a9f62bedbc3180aa\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f5ce2b85239fdb9a3356970c6260c9d2\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cf0ccee1833475d1cb7396abb7fb65e6\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c61ef63d7c9ec1a3ee93e590b45de2fc\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c25ad4285c7bd8c5dd49519f7e5c945e\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2f84cf2f96435ec804791141c96df76b\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3717f1a451d9bcf27642825fbe7afa39\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\751ae34e3a712b5e1298adedc65bdc1a\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\aaabba528f4a5dd75f54284f67efa05a\transformed\ui-core-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:hermes-android:0.80.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3c0efba9cf138cb926c92ca55f9b23c1\transformed\hermes-android-0.80.2-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9e64078f19daee46261c4647ea4224d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e5402ee6f91b243331db49e1dc2a0d9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\638e1ac7f2a1838808e5dd02d340c8e0\transformed\tracing-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a8ea06bd27551c92c6150523ea622ae0\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f9fb55b321a3c9ae661c590286a4aeee\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\776608539c9308fcf049062f2f2f9c59\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ca407b0cdd248fb252b0fad61eb447cc\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\157f750c030a43899aaeec479938e142\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\51dbd9ae21c085b2cb843db84b5d6696\transformed\fbjni-0.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ebf989c3089193ed7ea45d3e74380e1\transformed\soloader-0.12.1\AndroidManifest.xml:2:1-17:12
	package
		INJECTED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\debug\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:3:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:3:22-64
application
ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:5:5-25:19
MERGED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:5:5-25:19
MERGED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:5:5-25:19
INJECTED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\debug\AndroidManifest.xml:5:5-8:50
MERGED from [com.facebook.react:react-android:0.80.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5d0a6fceadea979a29a63bd98787fde9\transformed\react-android-0.80.2-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.80.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5d0a6fceadea979a29a63bd98787fde9\transformed\react-android-0.80.2-debug\AndroidManifest.xml:18:5-22:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fed6fbb2983fb7accf714336d7728246\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fed6fbb2983fb7accf714336d7728246\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0753d282651e5378a9f62bedbc3180aa\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0753d282651e5378a9f62bedbc3180aa\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cf0ccee1833475d1cb7396abb7fb65e6\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cf0ccee1833475d1cb7396abb7fb65e6\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9e64078f19daee46261c4647ea4224d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9e64078f19daee46261c4647ea4224d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e5402ee6f91b243331db49e1dc2a0d9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e5402ee6f91b243331db49e1dc2a0d9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\776608539c9308fcf049062f2f2f9c59\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\776608539c9308fcf049062f2f2f9c59\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ebf989c3089193ed7ea45d3e74380e1\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ebf989c3089193ed7ea45d3e74380e1\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0753d282651e5378a9f62bedbc3180aa\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:12:7-33
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:12:7-33
	android:label
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:7:7-39
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:7:7-39
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\debug\AndroidManifest.xml:8:9-48
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:9:7-52
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:9:7-52
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\debug\AndroidManifest.xml:7:9-29
	android:icon
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:8:7-41
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:8:7-41
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:10:7-34
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:10:7-34
	android:theme
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:11:7-38
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:11:7-38
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\debug\AndroidManifest.xml:6:9-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:6:7-38
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:6:7-38
activity#com.studdbapp.MainActivity
ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:13:7-24:18
	android:label
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:15:9-41
	android:launchMode
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:17:9-40
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:18:9-51
	android:exported
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:19:9-32
	android:configChanges
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:16:9-118
	android:name
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:14:9-37
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:20:9-23:25
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:21:13-65
	android:name
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:21:21-62
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:22:13-73
	android:name
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\main\AndroidManifest.xml:22:23-70
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\debug\AndroidManifest.xml
MERGED from [com.facebook.react:react-android:0.80.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5d0a6fceadea979a29a63bd98787fde9\transformed\react-android-0.80.2-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.80.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5d0a6fceadea979a29a63bd98787fde9\transformed\react-android-0.80.2-debug\AndroidManifest.xml:10:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f43af4eaa533d8678c3d8bedb383ac4e\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f43af4eaa533d8678c3d8bedb383ac4e\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\97726a531d160aa8efd424c23b47b0e7\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\97726a531d160aa8efd424c23b47b0e7\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bb1509f867cc8241c6321ad780ab9b40\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bb1509f867cc8241c6321ad780ab9b40\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a09cdeb31c1a69b0827dc865c184afd8\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a09cdeb31c1a69b0827dc865c184afd8\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8eb01e9cfdf7fc987f8c5c9f670c8ca7\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8eb01e9cfdf7fc987f8c5c9f670c8ca7\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\aa66091e09555cec570e10a7df1ce238\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\aa66091e09555cec570e10a7df1ce238\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bfc17559a383d0ef1d01959dc4d84b80\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bfc17559a383d0ef1d01959dc4d84b80\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\374e69babad10f8ad164f484a043f5cb\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\374e69babad10f8ad164f484a043f5cb\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c1754bbfa950701b23fefef6bf796ceb\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c1754bbfa950701b23fefef6bf796ceb\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4ac248b3671de32448570b9644e52940\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4ac248b3671de32448570b9644e52940\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2f98baf551fbb7a6d5cea4f26d7a8ec5\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2f98baf551fbb7a6d5cea4f26d7a8ec5\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8e522eca61febf8143e236f9d72207df\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8e522eca61febf8143e236f9d72207df\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9377fa07eef938bc503b47a41797d20\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9377fa07eef938bc503b47a41797d20\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04248846d45c7715aa716d408915667f\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04248846d45c7715aa716d408915667f\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8b09f32e8fc80fb8f8e561c9746f2051\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8b09f32e8fc80fb8f8e561c9746f2051\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\565be54688d002c2e6b94da854cf01b0\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\565be54688d002c2e6b94da854cf01b0\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\438600a2e90ac0d8713b620024e63e26\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\438600a2e90ac0d8713b620024e63e26\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8aa569497c94a1eda85ac3cb798a1ad7\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8aa569497c94a1eda85ac3cb798a1ad7\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2a0da4efd780d1d387ac0b6a795ff313\transformed\activity-1.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2a0da4efd780d1d387ac0b6a795ff313\transformed\activity-1.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c9e8870c85c6f7137997dc0b4d1a588e\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c9e8870c85c6f7137997dc0b4d1a588e\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\30c3f3a18f8f5396c69acef992d82cc6\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\30c3f3a18f8f5396c69acef992d82cc6\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0bbf34456e237ce1a6931dbc99635f40\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0bbf34456e237ce1a6931dbc99635f40\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\241e54545b49a632ffde793c1862f3d7\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\241e54545b49a632ffde793c1862f3d7\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fed6fbb2983fb7accf714336d7728246\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fed6fbb2983fb7accf714336d7728246\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f9d6938b3bbb51e2b58870b44d6c01ef\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f9d6938b3bbb51e2b58870b44d6c01ef\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bc67299e5d7a731320ef200998f51e11\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bc67299e5d7a731320ef200998f51e11\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f008d7eaa67ee21ccc9979bf9a561d17\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f008d7eaa67ee21ccc9979bf9a561d17\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\50081fb9fd213c2025efe5a44258cd6a\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\50081fb9fd213c2025efe5a44258cd6a\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec40a90505dc8ff078e92d445c422ca7\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec40a90505dc8ff078e92d445c422ca7\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\514f6bce29f0ded98ca5a500375fbcdb\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\514f6bce29f0ded98ca5a500375fbcdb\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1326c6b094cf38787ffe987c60d9a487\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1326c6b094cf38787ffe987c60d9a487\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0397ba8402f4881c1bbcde129a842107\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0397ba8402f4881c1bbcde129a842107\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0753d282651e5378a9f62bedbc3180aa\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0753d282651e5378a9f62bedbc3180aa\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f5ce2b85239fdb9a3356970c6260c9d2\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f5ce2b85239fdb9a3356970c6260c9d2\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cf0ccee1833475d1cb7396abb7fb65e6\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cf0ccee1833475d1cb7396abb7fb65e6\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c61ef63d7c9ec1a3ee93e590b45de2fc\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c61ef63d7c9ec1a3ee93e590b45de2fc\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c25ad4285c7bd8c5dd49519f7e5c945e\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c25ad4285c7bd8c5dd49519f7e5c945e\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2f84cf2f96435ec804791141c96df76b\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2f84cf2f96435ec804791141c96df76b\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3717f1a451d9bcf27642825fbe7afa39\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3717f1a451d9bcf27642825fbe7afa39\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\751ae34e3a712b5e1298adedc65bdc1a\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\751ae34e3a712b5e1298adedc65bdc1a\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\aaabba528f4a5dd75f54284f67efa05a\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\aaabba528f4a5dd75f54284f67efa05a\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.80.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3c0efba9cf138cb926c92ca55f9b23c1\transformed\hermes-android-0.80.2-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.80.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3c0efba9cf138cb926c92ca55f9b23c1\transformed\hermes-android-0.80.2-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9e64078f19daee46261c4647ea4224d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9e64078f19daee46261c4647ea4224d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e5402ee6f91b243331db49e1dc2a0d9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e5402ee6f91b243331db49e1dc2a0d9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\638e1ac7f2a1838808e5dd02d340c8e0\transformed\tracing-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\638e1ac7f2a1838808e5dd02d340c8e0\transformed\tracing-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a8ea06bd27551c92c6150523ea622ae0\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a8ea06bd27551c92c6150523ea622ae0\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f9fb55b321a3c9ae661c590286a4aeee\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f9fb55b321a3c9ae661c590286a4aeee\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\776608539c9308fcf049062f2f2f9c59\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\776608539c9308fcf049062f2f2f9c59\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ca407b0cdd248fb252b0fad61eb447cc\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ca407b0cdd248fb252b0fad61eb447cc\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\157f750c030a43899aaeec479938e142\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\157f750c030a43899aaeec479938e142\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\51dbd9ae21c085b2cb843db84b5d6696\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\51dbd9ae21c085b2cb843db84b5d6696\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ebf989c3089193ed7ea45d3e74380e1\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ebf989c3089193ed7ea45d3e74380e1\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\src\debug\AndroidManifest.xml
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from [com.facebook.react:react-android:0.80.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5d0a6fceadea979a29a63bd98787fde9\transformed\react-android-0.80.2-debug\AndroidManifest.xml:16:5-78
	android:name
		ADDED from [com.facebook.react:react-android:0.80.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5d0a6fceadea979a29a63bd98787fde9\transformed\react-android-0.80.2-debug\AndroidManifest.xml:16:22-75
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.80.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5d0a6fceadea979a29a63bd98787fde9\transformed\react-android-0.80.2-debug\AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.80.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5d0a6fceadea979a29a63bd98787fde9\transformed\react-android-0.80.2-debug\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.80.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5d0a6fceadea979a29a63bd98787fde9\transformed\react-android-0.80.2-debug\AndroidManifest.xml:20:13-77
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fed6fbb2983fb7accf714336d7728246\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cf0ccee1833475d1cb7396abb7fb65e6\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cf0ccee1833475d1cb7396abb7fb65e6\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9e64078f19daee46261c4647ea4224d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9e64078f19daee46261c4647ea4224d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e5402ee6f91b243331db49e1dc2a0d9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e5402ee6f91b243331db49e1dc2a0d9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fed6fbb2983fb7accf714336d7728246\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fed6fbb2983fb7accf714336d7728246\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fed6fbb2983fb7accf714336d7728246\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fed6fbb2983fb7accf714336d7728246\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fed6fbb2983fb7accf714336d7728246\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fed6fbb2983fb7accf714336d7728246\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fed6fbb2983fb7accf714336d7728246\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0753d282651e5378a9f62bedbc3180aa\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0753d282651e5378a9f62bedbc3180aa\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0753d282651e5378a9f62bedbc3180aa\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.studdbapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0753d282651e5378a9f62bedbc3180aa\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0753d282651e5378a9f62bedbc3180aa\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0753d282651e5378a9f62bedbc3180aa\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0753d282651e5378a9f62bedbc3180aa\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0753d282651e5378a9f62bedbc3180aa\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.studdbapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0753d282651e5378a9f62bedbc3180aa\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0753d282651e5378a9f62bedbc3180aa\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cf0ccee1833475d1cb7396abb7fb65e6\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cf0ccee1833475d1cb7396abb7fb65e6\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cf0ccee1833475d1cb7396abb7fb65e6\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9e64078f19daee46261c4647ea4224d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9e64078f19daee46261c4647ea4224d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9e64078f19daee46261c4647ea4224d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9e64078f19daee46261c4647ea4224d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9e64078f19daee46261c4647ea4224d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9e64078f19daee46261c4647ea4224d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9e64078f19daee46261c4647ea4224d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9e64078f19daee46261c4647ea4224d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9e64078f19daee46261c4647ea4224d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9e64078f19daee46261c4647ea4224d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9e64078f19daee46261c4647ea4224d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9e64078f19daee46261c4647ea4224d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9e64078f19daee46261c4647ea4224d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9e64078f19daee46261c4647ea4224d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9e64078f19daee46261c4647ea4224d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9e64078f19daee46261c4647ea4224d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9e64078f19daee46261c4647ea4224d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9e64078f19daee46261c4647ea4224d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9e64078f19daee46261c4647ea4224d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9e64078f19daee46261c4647ea4224d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9e64078f19daee46261c4647ea4224d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
meta-data#com.facebook.soloader.enabled
ADDED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ebf989c3089193ed7ea45d3e74380e1\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
	android:value
		ADDED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ebf989c3089193ed7ea45d3e74380e1\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8ebf989c3089193ed7ea45d3e74380e1\transformed\soloader-0.12.1\AndroidManifest.xml:13:13-57
