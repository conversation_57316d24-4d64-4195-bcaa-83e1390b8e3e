# C/C++ build system timings
generate_cxx_metadata
  [gap of 73ms]
  create-invalidation-state 29ms
  generate-prefab-packages
    [gap of 75ms]
    exec-prefab 2893ms
    [gap of 100ms]
  generate-prefab-packages completed in 3068ms
  execute-generate-process
    [gap of 20ms]
    exec-configure 6516ms
    [gap of 264ms]
  execute-generate-process completed in 6800ms
  [gap of 178ms]
  write-metadata-json-to-file 31ms
generate_cxx_metadata completed in 10206ms

