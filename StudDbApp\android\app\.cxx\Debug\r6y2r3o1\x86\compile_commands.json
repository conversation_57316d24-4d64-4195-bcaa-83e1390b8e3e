[{"directory": "C:/Users/<USER>/Desktop/React-N apps/stud_db_v2/StudDbApp/android/app/.cxx/Debug/r6y2r3o1/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dappmodules_EXPORTS -I\"C:/Users/<USER>/Desktop/React-N apps/stud_db_v2/StudDbApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup\" -I\"C:/Users/<USER>/Desktop/React-N apps/stud_db_v2/StudDbApp/android/app/build/generated/autolinking/src/main/jni\" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -o CMakeFiles\\appmodules.dir\\C_\\Users\\hu982\\Desktop\\React-N_apps\\stud_db_v2\\StudDbApp\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\React-N apps\\stud_db_v2\\StudDbApp\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\React-N apps\\stud_db_v2\\StudDbApp\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp"}, {"directory": "C:/Users/<USER>/Desktop/React-N apps/stud_db_v2/StudDbApp/android/app/.cxx/Debug/r6y2r3o1/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dappmodules_EXPORTS -I\"C:/Users/<USER>/Desktop/React-N apps/stud_db_v2/StudDbApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup\" -I\"C:/Users/<USER>/Desktop/React-N apps/stud_db_v2/StudDbApp/android/app/build/generated/autolinking/src/main/jni\" -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -o CMakeFiles\\appmodules.dir\\OnLoad.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\React-N apps\\stud_db_v2\\StudDbApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\React-N apps\\stud_db_v2\\StudDbApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp"}]