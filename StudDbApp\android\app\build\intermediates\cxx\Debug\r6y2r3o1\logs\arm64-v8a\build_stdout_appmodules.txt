ninja: Entering directory `C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\.cxx\Debug\r6y2r3o1\arm64-v8a'
[0/2] Re-checking globbed directories...
[1/3] Building CXX object CMakeFiles/appmodules.dir/428978d649c09f782f244db3f90fe291/StudDbApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[2/3] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[3/3] Linking CXX shared library "C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\build\intermediates\cxx\Debug\r6y2r3o1\obj\arm64-v8a\libappmodules.so"
