{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "appmodules", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-967f8bf4266a23816ec8.json", "name": "appmodules", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/React-N apps/stud_db_v2/StudDbApp/android/app/.cxx/Debug/r6y2r3o1/armeabi-v7a", "source": "C:/Users/<USER>/Desktop/React-N apps/stud_db_v2/StudDbApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}