# C/C++ build system timings
generate_cxx_metadata
  [gap of 149ms]
  create-invalidation-state 16ms
  generate-prefab-packages
    [gap of 33ms]
    exec-prefab 1918ms
    [gap of 87ms]
  generate-prefab-packages completed in 2038ms
  execute-generate-process
    [gap of 15ms]
    exec-configure 5581ms
    [gap of 422ms]
  execute-generate-process completed in 6018ms
  [gap of 295ms]
  write-metadata-json-to-file 112ms
  [gap of 16ms]
generate_cxx_metadata completed in 8675ms

