# CMAKE generated file: DO NOT EDIT!
# Generated by CMake Version 3.22
cmake_policy(SET CMP0009 NEW)

# input_SRC at C:/Users/<USER>/Desktop/React-N apps/stud_db_v2/StudDbApp/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:55 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/Users/<USER>/Desktop/React-N apps/stud_db_v2/StudDbApp/android/app/build/generated/autolinking/src/main/jni/*.cpp")
set(OLD_GLOB
  "C:/Users/<USER>/Desktop/React-N apps/stud_db_v2/StudDbApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- <PERSON><PERSON><PERSON><PERSON> mismatch!")
  file(TOUCH_NOCREATE "C:/Users/<USER>/Desktop/React-N apps/stud_db_v2/StudDbApp/android/app/.cxx/Debug/r6y2r3o1/x86/CMakeFiles/cmake.verify_globs")
endif()

# override_cpp_SRC at C:/Users/<USER>/Desktop/React-N apps/stud_db_v2/StudDbApp/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:50 (file)
# input_SRC at C:/Users/<USER>/Desktop/React-N apps/stud_db_v2/StudDbApp/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:55 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/Users/<USER>/Desktop/React-N apps/stud_db_v2/StudDbApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/*.cpp")
set(OLD_GLOB
  "C:/Users/<USER>/Desktop/React-N apps/stud_db_v2/StudDbApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/Users/<USER>/Desktop/React-N apps/stud_db_v2/StudDbApp/android/app/.cxx/Debug/r6y2r3o1/x86/CMakeFiles/cmake.verify_globs")
endif()
