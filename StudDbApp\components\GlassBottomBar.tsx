import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Platform,
} from 'react-native';

interface TabItem {
  id: string;
  label: string;
  icon: string;
}

interface GlassBottomBarProps {
  activeTab: string;
  onTabPress: (tabId: string) => void;
  tabs?: TabItem[];
}

const defaultTabs: TabItem[] = [
  { id: 'home', label: 'Home', icon: '🏠' },
  { id: 'search', label: 'Search', icon: '🔍' },
  { id: 'profile', label: 'Profile', icon: '👤' },
  { id: 'settings', label: 'Settings', icon: '⚙️' },
];

const GlassBottomBar: React.FC<GlassBottomBarProps> = ({
  activeTab,
  onTabPress,
  tabs = defaultTabs,
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.glassContainer}>
        <View style={styles.tabContainer}>
          {tabs.map((tab) => (
            <TouchableOpacity
              key={tab.id}
              style={[
                styles.tabItem,
                activeTab === tab.id && styles.activeTabItem,
              ]}
              onPress={() => onTabPress(tab.id)}
              activeOpacity={0.7}
            >
              <View style={[
                styles.iconContainer,
                activeTab === tab.id && styles.activeIconContainer,
              ]}>
                <Text style={[
                  styles.icon,
                  activeTab === tab.id && styles.activeIcon,
                ]}>
                  {tab.icon}
                </Text>
              </View>
              <Text style={[
                styles.label,
                activeTab === tab.id && styles.activeLabel,
              ]}>
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingBottom: Platform.OS === 'ios' ? 34 : 20, // Safe area for iOS
  },
  glassContainer: {
    borderTopLeftRadius: 25,
    borderTopRightRadius: 25,
    overflow: 'hidden',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderTopWidth: 1,
    borderLeftWidth: 1,
    borderRightWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -5,
    },
    shadowOpacity: 0.15,
    shadowRadius: 15,
    elevation: 10,
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 15,
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  tabItem: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 15,
  },
  activeTabItem: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    transform: [{ scale: 1.05 }],
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  activeIconContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  icon: {
    fontSize: 20,
    textAlign: 'center',
  },
  activeIcon: {
    fontSize: 22,
  },
  label: {
    fontSize: 11,
    fontWeight: '500',
    color: 'rgba(0, 0, 0, 0.6)',
    textAlign: 'center',
  },
  activeLabel: {
    color: 'rgba(0, 0, 0, 0.8)',
    fontWeight: '600',
    fontSize: 12,
  },
});

export default GlassBottomBar;
