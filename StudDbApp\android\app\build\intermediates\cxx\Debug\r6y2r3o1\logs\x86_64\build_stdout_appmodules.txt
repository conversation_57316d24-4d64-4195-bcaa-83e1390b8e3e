ninja: Entering directory `C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\.cxx\Debug\r6y2r3o1\x86_64'
[0/2] Re-checking globbed directories...
[1/3] Building CXX object CMakeFiles/appmodules.dir/44929975967d1ad00ae56406d275cdb7/stud_db_v2/StudDbApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[2/3] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[3/3] Linking CXX shared library "C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\android\app\build\intermediates\cxx\Debug\r6y2r3o1\obj\x86_64\libappmodules.so"
