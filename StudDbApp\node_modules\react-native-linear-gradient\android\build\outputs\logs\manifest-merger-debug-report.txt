-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\node_modules\react-native-linear-gradient\android\src\main\AndroidManifest.xml:1:1-45
INJECTED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\node_modules\react-native-linear-gradient\android\src\main\AndroidManifest.xml:1:1-45
	package
		ADDED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\node_modules\react-native-linear-gradient\android\src\main\AndroidManifest.xml:1:11-42
		INJECTED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\node_modules\react-native-linear-gradient\android\src\main\AndroidManifest.xml
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\node_modules\react-native-linear-gradient\android\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\node_modules\react-native-linear-gradient\android\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\node_modules\react-native-linear-gradient\android\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\node_modules\react-native-linear-gradient\android\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\React-N apps\stud_db_v2\StudDbApp\node_modules\react-native-linear-gradient\android\src\main\AndroidManifest.xml
