{"logs": [{"outputFile": "com.studdbapp-mergeDebugResources-26:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\97726a531d160aa8efd424c23b47b0e7\\transformed\\appcompat-1.7.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,49", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,4493", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,4568"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\5d0a6fceadea979a29a63bd98787fde9\\transformed\\react-android-0.80.2-debug\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,201,272,342,425,496,563,640,720,805,885,955,1038,1123,1198,1283,1369,1446,1520,1591,1678,1748,1827,1902", "endColumns": "68,76,70,69,82,70,66,76,79,84,79,69,82,84,74,84,85,76,73,70,86,69,78,74,76", "endOffsets": "119,196,267,337,420,491,558,635,715,800,880,950,1033,1118,1193,1278,1364,1441,1515,1586,1673,1743,1822,1897,1974"}, "to": {"startLines": "29,37,38,39,40,41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2797,3579,3656,3727,3797,3880,3951,4018,4095,4175,4260,4340,4410,4573,4658,4733,4818,4904,4981,5055,5126,5314,5384,5463,5538", "endColumns": "68,76,70,69,82,70,66,76,79,84,79,69,82,84,74,84,85,76,73,70,86,69,78,74,76", "endOffsets": "2861,3651,3722,3792,3875,3946,4013,4090,4170,4255,4335,4405,4488,4653,4728,4813,4899,4976,5050,5121,5208,5379,5458,5533,5610"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\0753d282651e5378a9f62bedbc3180aa\\transformed\\core-1.13.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "30,31,32,33,34,35,36,58", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2866,2963,3065,3163,3260,3362,3468,5213", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "2958,3060,3158,3255,3357,3463,3574,5309"}}]}]}