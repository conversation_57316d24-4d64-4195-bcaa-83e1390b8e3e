{"buildFiles": ["C:\\Users\\<USER>\\Desktop\\React-N apps\\stud_db_v2\\StudDbApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\React-N apps\\stud_db_v2\\StudDbApp\\android\\app\\.cxx\\Debug\\r6y2r3o1\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\React-N apps\\stud_db_v2\\StudDbApp\\android\\app\\.cxx\\Debug\\r6y2r3o1\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"appmodules::@6890427a1f51a3e7e1df": {"artifactName": "appmodules", "abi": "x86_64", "output": "C:\\Users\\<USER>\\Desktop\\React-N apps\\stud_db_v2\\StudDbApp\\android\\app\\build\\intermediates\\cxx\\Debug\\r6y2r3o1\\obj\\x86_64\\libappmodules.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\51dbd9ae21c085b2cb843db84b5d6696\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\5d0a6fceadea979a29a63bd98787fde9\\transformed\\react-android-0.80.2-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\5d0a6fceadea979a29a63bd98787fde9\\transformed\\react-android-0.80.2-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so"]}}}