{"logs": [{"outputFile": "com.studdbapp-mergeDebugResources-26:/values-ta/values-ta.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\97726a531d160aa8efd424c23b47b0e7\\transformed\\appcompat-1.7.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,2871", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,2948"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,49", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,4602", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,4679"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\5d0a6fceadea979a29a63bd98787fde9\\transformed\\react-android-0.80.2-debug\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,210,282,350,434,504,571,647,725,807,887,958,1040,1122,1200,1289,1379,1460,1532,1602,1696,1771,1854,1923", "endColumns": "74,79,71,67,83,69,66,75,77,81,79,70,81,81,77,88,89,80,71,69,93,74,82,68,77", "endOffsets": "125,205,277,345,429,499,566,642,720,802,882,953,1035,1117,1195,1284,1374,1455,1527,1597,1691,1766,1849,1918,1996"}, "to": {"startLines": "29,37,38,39,40,41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2871,3692,3772,3844,3912,3996,4066,4133,4209,4287,4369,4449,4520,4684,4766,4844,4933,5023,5104,5176,5246,5441,5516,5599,5668", "endColumns": "74,79,71,67,83,69,66,75,77,81,79,70,81,81,77,88,89,80,71,69,93,74,82,68,77", "endOffsets": "2941,3767,3839,3907,3991,4061,4128,4204,4282,4364,4444,4515,4597,4761,4839,4928,5018,5099,5171,5241,5335,5511,5594,5663,5741"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\0753d282651e5378a9f62bedbc3180aa\\transformed\\core-1.13.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "30,31,32,33,34,35,36,58", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2946,3042,3145,3244,3342,3449,3564,5340", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "3037,3140,3239,3337,3444,3559,3687,5436"}}]}]}