# C/C++ build system timings
generate_cxx_metadata
  [gap of 81ms]
  create-invalidation-state 38ms
  generate-prefab-packages
    [gap of 32ms]
    exec-prefab 1596ms
    [gap of 66ms]
  generate-prefab-packages completed in 1694ms
  execute-generate-process
    [gap of 10ms]
    exec-configure 7639ms
    [gap of 280ms]
  execute-generate-process completed in 7929ms
  [gap of 39ms]
  remove-unexpected-so-files 24ms
  [gap of 459ms]
  write-metadata-json-to-file 70ms
generate_cxx_metadata completed in 10362ms

